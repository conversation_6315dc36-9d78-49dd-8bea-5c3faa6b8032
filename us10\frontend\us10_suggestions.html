<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Resume Suggestions - Dr. Resume</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/us10_styles.css') }}">
    <style>
        /* Additional suggestions-specific styles */
        .suggestions-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            color: white;
            padding: 30px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .content {
            padding: 40px;
        }

        /* Loading spinner animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Suggestion Categories - Card Style */
        .suggestion-category {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.2s ease;
            min-height: 180px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: relative;
            resize: vertical;
            overflow: hidden;
            max-height: 600px;
            display: flex;
            flex-direction: column;
        }

        /* Category title styling */
        .category-title {
            flex-shrink: 0;
            margin-bottom: 15px;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #7c3aed;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        /* Category content - expandable area */
        .category-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 5px;
            margin-bottom: 25px; /* Space for resize handle */
            color: #374151;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Custom scrollbar for content */
        .category-content::-webkit-scrollbar {
            width: 6px;
        }

        .category-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .category-content::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .category-content::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .card-resize-handle {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 20px;
            height: 20px;
            background: linear-gradient(-45deg, transparent 0%, transparent 30%, #cbd5e1 30%, #cbd5e1 35%, transparent 35%, transparent 45%, #cbd5e1 45%, #cbd5e1 50%, transparent 50%, transparent 60%, #cbd5e1 60%, #cbd5e1 65%, transparent 65%);
            cursor: nw-resize;
            border-bottom-right-radius: 12px;
            opacity: 0.7;
            transition: all 0.2s ease;
            z-index: 10;
        }

        .card-resize-handle:hover {
            opacity: 1;
            background: linear-gradient(-45deg, transparent 0%, transparent 30%, #7c3aed 30%, #7c3aed 35%, transparent 35%, transparent 45%, #7c3aed 45%, #7c3aed 50%, transparent 50%, transparent 60%, #7c3aed 60%, #7c3aed 65%, transparent 65%);
            transform: scale(1.1);
        }

        .card-resize-handle::before {
            content: '';
            position: absolute;
            bottom: 3px;
            right: 3px;
            width: 3px;
            height: 3px;
            background: currentColor;
            border-radius: 50%;
        }

        .card-resize-handle::after {
            content: '';
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 3px;
            height: 3px;
            background: currentColor;
            border-radius: 50%;
        }

        /* Resize cursor for individual cards */
        .suggestion-category:hover {
            border-color: #7c3aed;
        }

        .suggestion-category.resizing {
            user-select: none;
            transition: none;
        }

        /* Responsive adjustments for resize handles */
        @media (max-width: 768px) {
            .card-resize-handle {
                width: 24px;
                height: 24px;
            }

            .suggestion-category {
                min-height: 150px;
                max-height: 500px;
            }
        }

        /* Improved visual feedback */
        .card-resize-handle:active {
            background: linear-gradient(-45deg, transparent 0%, transparent 40%, #6d28d9 40%, #6d28d9 60%, transparent 60%);
        }

        .suggestion-category:hover {
            border-color: #7c3aed;
            box-shadow: 0 8px 25px rgba(124, 58, 237, 0.15);
            transform: translateY(-2px);
        }



        /* Clean Suggestion Cards */
        .suggestion-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .suggestion-card:hover {
            border-color: #7c3aed;
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.15);
            transform: translateY(-1px);
        }

        .suggestion-header {
            padding: 20px 24px 16px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .suggestion-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .priority-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-critical {
            background: #fee2e2;
            color: #dc2626;
        }

        .priority-high {
            background: #fef3c7;
            color: #d97706;
        }

        .priority-medium {
            background: #dbeafe;
            color: #2563eb;
        }

        .priority-low {
            background: #d1fae5;
            color: #059669;
        }

        .suggestion-content {
            padding: 0 24px 20px;
        }

        .suggestion-description {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .suggestion-action {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: #374151;
            line-height: 1.4;
        }

        .suggestion-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 12px;
        }

        .keyword-tag {
            background: #7c3aed;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        /* Category Sections */
        .category-section {
            margin-bottom: 32px;
        }

        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding: 16px 0;
            border-bottom: 2px solid #f3f4f6;
        }



        .category-count {
            background: #7c3aed;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #9ca3af;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .suggestion-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .category-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }



        .suggestion-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .suggestion-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #7c3aed;
            transform: translateY(-1px);
        }

        .suggestion-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .suggestion-description {
            color: #6b7280;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .suggestion-action {
            background: #f0f9ff;
            border-left: 3px solid #0ea5e9;
            padding: 8px 12px;
            margin: 8px 0;
            border-radius: 4px;
            font-size: 13px;
        }

        .suggestion-keywords {
            margin: 8px 0;
        }

        .keywords-list {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .keyword-tag {
            background: #7c3aed;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .suggestion-placement {
            color: #6b7280;
            font-size: 12px;
            margin: 4px 0;
        }

        .suggestion-example {
            background: #f0fdf4;
            border-left: 3px solid #22c55e;
            padding: 8px 12px;
            margin: 8px 0;
            border-radius: 4px;
            font-size: 13px;
        }

        .suggestion-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .implement-btn {
            font-size: 12px;
            padding: 4px 12px;
        }

        .category-count {
            background: #7c3aed;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        /* Priority colors - Clean design */
        .suggestion-item.critical {
            border-left: 4px solid #dc2626;
            background: linear-gradient(90deg, #fef2f2 0%, white 10%);
        }

        .suggestion-item.high {
            border-left: 4px solid #f59e0b;
            background: linear-gradient(90deg, #fffbeb 0%, white 10%);
        }

        .suggestion-item.medium {
            border-left: 4px solid #0ea5e9;
            background: linear-gradient(90deg, #f0f9ff 0%, white 10%);
        }

        .suggestion-item.low {
            border-left: 4px solid #22c55e;
            background: linear-gradient(90deg, #f0fdf4 0%, white 10%);
        }

        /* Premium categories */
        .premium-category {
            border-color: #f59e0b;
            background: linear-gradient(135deg, #fef3c7 0%, #ffffff 100%);
        }

        .premium-category .category-title {
            color: #f59e0b;
        }

        /* Button hover effects */
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Alert styles */
        .alert {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            border: none;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
        }

        .alert-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .alert-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body class="dashboard-body">
    <div class="suggestions-container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-user-md"></i>Dr. Resume - AI Suggestions
            </h1>
            <nav class="nav">
                <a href="/dashboard" class="nav-link">📊 Dashboard</a>
                <a href="/suggestions" class="nav-link active">💡 Suggestions</a>
                <a href="/account" class="nav-link">⚙️ Account</a>
                <a href="#" class="nav-link" id="logoutBtn" title="Logout from your account">🚪 Logout</a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Welcome Section -->
            <div style="text-align: center; margin-bottom: 40px;">
                <h2 style="color: #1f2937; margin-bottom: 12px; font-size: 24px; font-weight: 600;">
                    <i class="fas fa-lightbulb" style="color: #7c3aed; margin-right: 8px;"></i>
                    AI Resume Suggestions
                </h2>
                <p style="color: #6b7280; font-size: 16px; line-height: 1.5;">
                    Get personalized suggestions to improve your resume and increase your chances of landing your dream job.
                </p>
            </div>

            <!-- Alert Container -->
            <div id="alertContainer" style="margin-bottom: 20px;"></div>

            <!-- Selection Form -->
            <div style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-bottom: 30px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
                    <div>
                        <label for="resumeSelect" style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">
                            Select Resume:
                        </label>
                        <select id="resumeSelect" name="resumeSelect" style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; background: white;" autocomplete="off" title="Choose a resume to analyze" aria-label="Select resume for analysis">
                            <option value="">Loading resumes...</option>
                        </select>
                    </div>
                    <div>
                        <label for="jobSelect" style="display: block; color: #374151; font-weight: 600; margin-bottom: 8px;">
                            Select Job Description:
                        </label>
                        <select id="jobSelect" name="jobSelect" style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; background: white;" autocomplete="off" title="Choose a job description to compare against" aria-label="Select job description for comparison">
                            <option value="">Loading job descriptions...</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                    <button id="generateBasicBtn" title="Generate basic AI-powered suggestions for your resume" style="padding: 16px 24px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%); color: white;">
                        <i class="fas fa-magic" style="margin-right: 8px;"></i>Generate Basic Suggestions
                    </button>
                    <button id="generatePremiumBtn" title="Generate premium AI-powered suggestions with advanced analysis" style="padding: 16px 24px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;">
                        <i class="fas fa-crown" style="margin-right: 8px;"></i>Generate Premium Suggestions
                    </button>
                </div>


            </div>

            <!-- Loading Spinner -->
            <div id="loadingSpinner" style="text-align: center; display: none; padding: 40px;">
                <div style="width: 40px; height: 40px; border: 4px solid #e5e7eb; border-top: 4px solid #7c3aed; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
                <p style="color: #6b7280; font-size: 16px;">Analyzing your resume and generating suggestions...</p>
            </div>

            <!-- Suggestions Container -->
            <div id="suggestionsContainer" style="display: none;">
                <!-- Matching Score Section -->
                <div id="matchingScoreSection" style="background: #e0f2fe; border-radius: 12px; padding: 30px; margin-bottom: 30px; border: 2px solid #0ea5e9;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 2px solid #0ea5e9;">
                        <h3 style="color: #1f2937; font-size: 20px; font-weight: 600; margin: 0;">
                            <i class="fas fa-chart-line" style="color: #0ea5e9; margin-right: 8px;"></i>
                            Resume-Job Match Analysis
                        </h3>
                    </div>
                    <div id="matchingScoreContent">
                        <!-- Score content will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Basic Suggestions - Initially Hidden -->
                <div id="basicSuggestionsCard" style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-bottom: 30px; display: none;">
                    <div class="category-header">
                        <h3 class="category-title">
                            <i class="fas fa-lightbulb" style="color: #7c3aed;"></i>
                            Basic Suggestions
                        </h3>
                        <span id="basicCount" class="category-count">0</span>
                    </div>
                    <div id="basicSuggestionsBody">
                        <!-- Basic Suggestions Grid Layout -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px;">
                            <!-- Technical Skills Card -->
                            <div class="suggestion-category" id="technicalSkillsBasic">
                                <div class="category-title">
                                    <i class="fas fa-code" style="color: #7c3aed;"></i>Technical Skills
                                </div>
                                <div class="category-content" id="technicalSkillsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Soft Skills Card -->
                            <div class="suggestion-category" id="softSkillsBasic">
                                <div class="category-title">
                                    <i class="fas fa-users" style="color: #7c3aed;"></i>Soft Skills
                                </div>
                                <div class="category-content" id="softSkillsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Industry Keywords Card -->
                            <div class="suggestion-category" id="industryKeywordsBasic">
                                <div class="category-title">
                                    <i class="fas fa-industry" style="color: #7c3aed;"></i>Industry Keywords
                                </div>
                                <div class="category-content" id="industryKeywordsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- ATS Optimization Card -->
                            <div class="suggestion-category" id="atsOptimizationBasic">
                                <div class="category-title">
                                    <i class="fas fa-robot" style="color: #7c3aed;"></i>ATS Optimization
                                </div>
                                <div class="category-content" id="atsOptimizationContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Structure Card -->
                            <div class="suggestion-category" id="structureBasic">
                                <div class="category-title">
                                    <i class="fas fa-sitemap" style="color: #7c3aed;"></i>Structure
                                </div>
                                <div class="category-content" id="structureContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Quick Wins Card -->
                            <div class="suggestion-category" id="quickWinsBasic">
                                <div class="category-title">
                                    <i class="fas fa-bolt" style="color: #7c3aed;"></i>Quick Wins
                                </div>
                                <div class="category-content" id="quickWinsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Premium Suggestions - Initially Hidden -->
                <div id="premiumSuggestionsCard" style="background: #fef3c7; border-radius: 12px; padding: 30px; margin-bottom: 30px; display: none;">
                    <div class="category-header">
                        <h3 class="category-title">
                            <i class="fas fa-crown" style="color: #f59e0b;"></i>
                            Premium Suggestions (10 Categories)
                        </h3>
                        <span id="premiumCount" class="category-count" style="background: #f59e0b;">0</span>
                    </div>
                    <div id="premiumSuggestionsBody">
                        <!-- Advanced Premium Features -->
                        <div style="margin-bottom: 20px;">
                            <h6 style="color: #1f2937; font-weight: 600; margin-bottom: 16px;">
                                <i class="fas fa-star" style="color: #f59e0b; margin-right: 8px;"></i>Advanced Premium Features
                            </h6>
                        </div>

                        <!-- Premium Categories Grid -->
                        <div style="display: grid; grid-template-columns: 1fr; gap: 16px;">
                            <!-- Enhanced Premium Categories -->
                            <div class="suggestion-category premium-category" id="technicalSkillsPremium">
                                <div class="category-title">
                                    <i class="fas fa-code" style="color: #f59e0b;"></i>Technical Skills Enhancement
                                </div>
                                <div class="category-content" id="technicalSkillsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Soft Skills -->
                            <div class="suggestion-category premium-category" id="softSkillsPremium">
                                <div class="category-title">
                                    <i class="fas fa-users" style="color: #f59e0b;"></i>Soft Skills Enhancement
                                </div>
                                <div class="category-content" id="softSkillsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Industry Keywords -->
                            <div class="suggestion-category premium-category" id="industryKeywordsPremium">
                                <div class="category-title">
                                    <i class="fas fa-tags" style="color: #f59e0b;"></i>Industry Keywords
                                </div>
                                <div class="category-content" id="industryKeywordsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- ATS Optimization -->
                            <div class="suggestion-category premium-category" id="atsOptimizationPremium">
                                <div class="category-title">
                                    <i class="fas fa-robot" style="color: #f59e0b;"></i>ATS Optimization
                                </div>
                                <div class="category-content" id="atsOptimizationContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Structure -->
                            <div class="suggestion-category premium-category" id="structurePremium">
                                <div class="category-title">
                                    <i class="fas fa-sitemap" style="color: #f59e0b;"></i>Resume Structure
                                </div>
                                <div class="category-content" id="structureContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Quick Wins -->
                            <div class="suggestion-category premium-category" id="quickWinsPremium">
                                <div class="category-title">
                                    <i class="fas fa-bolt" style="color: #f59e0b;"></i>Quick Wins
                                </div>
                                <div class="category-content" id="quickWinsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Critical Gaps -->
                            <div class="suggestion-category premium-category" id="criticalGapsPremium">
                                <div class="category-title">
                                    <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>Critical Gaps
                                </div>
                                <div class="category-content" id="criticalGapsContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Contextual Advice -->
                            <div class="suggestion-category premium-category" id="contextualAdvicePremium">
                                <div class="category-title">
                                    <i class="fas fa-lightbulb" style="color: #f59e0b;"></i>Contextual Advice
                                </div>
                                <div class="category-content" id="contextualAdviceContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Quantification -->
                            <div class="suggestion-category premium-category" id="quantificationPremium">
                                <div class="category-title">
                                    <i class="fas fa-chart-line" style="color: #f59e0b;"></i>Quantification
                                </div>
                                <div class="category-content" id="quantificationContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>

                            <!-- Skill Development -->
                            <div class="suggestion-category premium-category" id="skillDevelopmentPremium">
                                <div class="category-title">
                                    <i class="fas fa-graduation-cap" style="color: #f59e0b;"></i>Skill Development
                                </div>
                                <div class="category-content" id="skillDevelopmentContent">
                                    <p style="color: #6b7280;">No suggestions yet</p>
                                </div>
                                <div class="card-resize-handle" title="Drag to resize"></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- Premium Upgrade Modal -->
        <div class="modal fade" id="upgradeModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-warning">
                        <h5 class="modal-title">
                            <i class="fas fa-crown me-2"></i>Premium Features Required
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Unlock advanced AI-powered suggestions with our Premium plan:</p>
                        <ul>
                            <li><strong>Critical Gap Analysis</strong> - Identify missing skills that matter most</li>
                            <li><strong>Contextual Advice</strong> - Tailored recommendations based on job requirements</li>
                            <li><strong>Quantification Tips</strong> - Add impactful metrics to your achievements</li>
                            <li><strong>Skill Development Paths</strong> - Personalized learning recommendations</li>
                            <li><strong>Semantic Analysis</strong> - Advanced keyword optimization</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" title="Close premium upgrade modal">Maybe Later</button>
                        <button type="button" class="btn btn-warning" title="Upgrade to Premium plan">
                            <i class="fas fa-crown me-2"></i>Upgrade to Premium
                        </button>
                    </div>
                </div>
            </div>
        </div>

                <!-- Suggestion History -->
                <div id="historyCard" style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-top: 30px; display: none;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 2px solid #e5e7eb;">
                        <h3 style="color: #1f2937; font-size: 20px; font-weight: 600; margin: 0;">
                            <i class="fas fa-history" style="color: #7c3aed; margin-right: 8px;"></i>
                            Suggestion History
                        </h3>
                    </div>
                    <div id="historyBody">
                        <!-- History will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="background: #f8fafc; padding: 30px; text-align: center; margin-top: 40px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; margin: 0; font-size: 14px;">
                &copy; 2024 Dr. Resume - US-10 AI Suggestions Service<br>
                <span style="font-weight: 600; color: #7c3aed; margin-top: 5px; display: inline-block;">Made by MOHAMMAD TABISH</span>
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/static/js/us10_suggestions.js?v=1.0"></script>
</body>
</html>
